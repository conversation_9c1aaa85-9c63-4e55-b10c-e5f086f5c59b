<?php

namespace Agilize\Folha\PensaoAlimenticia\Domain\Model;


use Agilize\Administrative\Domain\Model\TimestampableEntity;
use Agilize\Dms\Domain\Model\Document;
use Apimenti\Account\Domain\Model\Administrative\SoftDeletableTrait;
use Apimenti\Account\Domain\Model\Company;
use Apimenti\Account\Domain\Model\Company\Partner;
use Apimenti\Account\Domain\Model\NaturalPerson\Dependencia;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use TYPO3\Flow\Resource\Resource;
use TYPO3\Flow\Annotations as Flow;
use Doctrine\ORM\Mapping as ORM;
use Gedmo\Mapping\Annotation as Gedmo;

/**
 * @Flow\Entity
 * @Gedmo\Loggable(logEntryClass="Agilize\Log\Domain\Model\LogEntry")
 */
class PensaoAlimenticia
{
    use TimestampableEntity, SoftDeletableTrait;

    /**
     * @var Company
     * @ORM\ManyToOne
     */
    protected $company;

    /**
     * @var Partner
     * @ORM\ManyToOne
     */
    protected $partner;

    /**
     * @var \Doctrine\Common\Collections\Collection<\Apimenti\Account\Domain\Model\NaturalPerson\Dependencia>
     * @ORM\OneToMany(mappedBy="pensaoAlimenticia")
     * @Gedmo\Versioned
     */
    protected $dependentes;

    /**
     * @see TipoPensaoAlimenticia
     * @var int
     */
    protected $tipoPensaoAlimenticia;

    /**
     * @var float
     */
    protected $valor;

    /**
     * @var bool
     */
    protected $deveDescontarNoContracheque;

    /**
     * @var Document
     * @ORM\OneToOne(cascade={"all"}, orphanRemoval=true)
     * @ORM\Column(nullable=true)
     * @Gedmo\Versioned
     */
    protected $arquivoDecisaoJudicial;

    /**
     * @var \DateTime
     * @ORM\Column(nullable=true)
     * @Gedmo\Versioned
     */
    protected $encerradoEm;

    /**
     * PensaoAlimenticia constructor.
     * @param Company $company
     * @param Partner $partner
     * @param float $valor
     * @param int $tipoPensaoAlimenticia
     * @param bool $deveDescontarNoContracheque
     * @param Resource $arquivoDecisaoJudicial
     */
    public function __construct(
        Company $company,
        Partner $partner,
        $valor,
        $tipoPensaoAlimenticia,
        $deveDescontarNoContracheque = false,
        Resource $arquivoDecisaoJudicial = null
    )
    {
        $this->company = $company;
        $this->partner = $partner;
        $this->valor = $valor;
        $this->tipoPensaoAlimenticia = $tipoPensaoAlimenticia;
        $this->deveDescontarNoContracheque = $deveDescontarNoContracheque;
        $this->arquivoDecisaoJudicial = $arquivoDecisaoJudicial;
        $this->dependentes = new ArrayCollection();
    }

    /**
     * @return Company
     */
    public function getCompany()
    {
        return $this->company;
    }

    /**
     * @return int
     */
    public function getTipoPensaoAlimenticia()
    {
        return $this->tipoPensaoAlimenticia;
    }

    /**
     * @return array
     */
    public function getTipoPensaoAlimenticiaObject()
    {
        return TipoPensaoAlimenticia::getOne($this->tipoPensaoAlimenticia);
    }

    /**
     * @return bool
     */
    public function isDecisaoJudicial()
    {
        return $this->tipoPensaoAlimenticia === TipoPensaoAlimenticia::DECISAO_JUDICIAL;
    }

    /**
     * @return \DateTime
     */
    public function getEncerradoEm()
    {
        return $this->encerradoEm;
    }

    /**
     * @return float
     */
    public function getValor()
    {
        return $this->valor;
    }

    /**
     * @return bool
     */
    public function getDeveDescontarNoContracheque()
    {
        return $this->deveDescontarNoContracheque;
    }

    /**
     * @return Document
     */
    public function getArquivoDecisaoJudicial()
    {
        return $this->arquivoDecisaoJudicial;
    }

    /**
     * @return Collection
     */
    public function getDependentes()
    {
        return $this->dependentes;
    }

    /**
     * @return Dependencia|null
     */
    public function getDependente()
    {
        return $this->dependentes->isEmpty() ? null : $this->dependentes->first();
    }

    /**
     * @return Partner
     */
    public function getPartner()
    {
        return $this->partner;
    }

    /**
     * @throws \Exception
     */
    public function encerrar()
    {
        $encerradoEm = new \DateTime();
        $this->encerradoEm = $encerradoEm;
    }

    /**
     * @param Document $arquivoDecisaoJudicial
     */
    public function setArquivoDecisaoJudicial($arquivoDecisaoJudicial)
    {
        $this->arquivoDecisaoJudicial = $arquivoDecisaoJudicial;
    }

    public function removerArquivoDecisaoJudicial() {
        $this->arquivoDecisaoJudicial = null;
    }

    /**
     * @param Company $company
     */
    public function setCompany($company)
    {
        $this->company = $company;
    }

    /**
     * @param Partner $partner
     */
    public function setPartner($partner)
    {
        $this->partner = $partner;
    }

    /**
     * @param int $tipoPensaoAlimenticia
     */
    public function setTipoPensaoAlimenticia($tipoPensaoAlimenticia)
    {
        $this->tipoPensaoAlimenticia = $tipoPensaoAlimenticia;
    }

    /**
     * @param float $valor
     */
    public function setValor($valor)
    {
        $this->valor = $valor;
    }

    /**
     * @param bool $deveDescontarNoContracheque
     */
    public function setDeveDescontarNoContracheque($deveDescontarNoContracheque)
    {
        $this->deveDescontarNoContracheque = $deveDescontarNoContracheque;
    }

    /**
     * @param \DateTime $encerradoEm
     */
    public function setEncerradoEm($encerradoEm)
    {
        $this->encerradoEm = $encerradoEm;
    }

    /**
     * @param Dependencia $dependente
     */
    public function addDependente(Dependencia $dependente)
    {
        if (!$this->dependentes->contains($dependente)) {
            $this->dependentes->add($dependente);
        }
    }

    /**
     * @param Dependencia $dependente
     */
    public function removeDependente(Dependencia $dependente)
    {
        $this->dependentes->removeElement($dependente);
    }

    /**
     * @param Dependencia|null $dependente
     */
    public function setDependente($dependente)
    {
        $this->dependentes->clear();
        if ($dependente instanceof Dependencia) {
            $this->dependentes->add($dependente);
        }
    }
}
