<?php

namespace Agilize\Folha\PensaoAlimenticia\Service;

use Agilize\Dms\Domain\Model\Document;
use Agilize\Dms\Domain\Model\DocumentFactory;
use Agilize\Dms\Domain\Model\DocumentMetadataType;
use Agilize\Dms\Domain\Model\DocumentType;
use Agilize\Dms\Domain\Repository\DocumentRepository;
use Agilize\Dms\Service\DmsService;
use Agilize\Folha\PensaoAlimenticia\Domain\Model\PensaoAlimenticia;
use Agilize\Folha\PensaoAlimenticia\Domain\Repository\PensaoAlimenticiaRepository;
use Apimenti\Account\Domain\Model\Company;
use Apimenti\Account\Domain\Model\Company\Partner;
use Apimenti\Account\Domain\Model\NaturalPerson\Dependencia;
use Apimenti\Account\Domain\Repository\NaturalPerson\DependenciaRepository;
use Carbon\Carbon;
use Doctrine\Common\Collections\ArrayCollection;
use TYPO3\Flow\Annotations as Flow;
use TYPO3\Flow\Persistence\Exception\IllegalObjectTypeException;
use TYPO3\Flow\Resource\Resource;
use TYPO3\Flow\Resource\ResourceManager;
use TYPO3\Flow\Persistence\PersistenceManagerInterface;

/**
 * @Flow\Scope("singleton")
 */
class PensaoAlimenticiaService
{
    /**
     * @var PensaoAlimenticiaRepository
     * @Flow\Inject
     */
    protected $pensaoAlimenticiaRepository;

    /**
     * @var DocumentFactory
     * @Flow\Inject
     */
    protected $documentFactory;
    /**
     * @var DocumentRepository
     * @Flow\Inject
     */
    protected $documentRepository;

    /**
     * @var ResourceManager
     * @Flow\Inject
     */
    protected $resourceManager;

    /**
     * @var DmsService
     * @Flow\Inject
     */
    protected $dmsService;

    /**
     * @var DependenciaRepository
     * @Flow\Inject
     */
    protected $dependenciaRepository;

    /**
     * @var PersistenceManagerInterface
     * @Flow\Inject
     */
    protected $persistenceManager;

    /**
     * @param Company $company
     * @return bool
     */
    public function temPensaoAlimenticiaAtivaQueDeveDescontarNoContracheque(Company $company)
    {
        return $this->pensaoAlimenticiaRepository->countPensoesAtivasQueDevemDescontarNoContrachequeByCompany($company) > 0;
    }

    /**
     * @param Company $company
     * @return bool
     */
    public function temPensaoAlimenticiaComDecisaoJudicial(Company $company)
    {
        return $this->pensaoAlimenticiaRepository->countPensoesAtivasComDecisaoJudicial($company) > 0;
    }

    /**
     * @param Company $company
     * @param Company\Partner $partner
     * @return ArrayCollection
     */
    public function listarPensoesByPartner(Company $company, Company\Partner $partner)
    {
        return $this->pensaoAlimenticiaRepository->findByCompanyAndPartner($company, $partner);
    }

    /**
     * @param Company $company
     * @param Company\Partner $partner
     * @param \DateTime $competence
     * @return ArrayCollection
     */
    public function listarPensoesByPartnerAndCompetence(Company $company, Company\Partner $partner, \DateTime $competence)
    {
        return $this->pensaoAlimenticiaRepository->findByCompanyAndPartnerAndCompetence($company, $partner, $competence);
    }

    /**
     * @param Company\Partner $partner
     * @return ArrayCollection
     */
    public function listarPensoesByPartnerNotClosed
    (
        Company\Partner $partner
    )
    {
        return $this->pensaoAlimenticiaRepository->findByPartnerNotClosed($partner);
    }

    /**
     * @param PensaoAlimenticia $pensaoAlimenticia
     * @throws \Exception
     */
    public function encerrar(PensaoAlimenticia $pensaoAlimenticia)
    {
        $pensaoAlimenticia->encerrar();
        $this->pensaoAlimenticiaRepository->update($pensaoAlimenticia);
    }

    /**
     * @param PensaoAlimenticia $pensaoAlimenticia
     * @param Resource|null $arquivoDecisaoJudicial
     * @param Dependencia|null $dependente
     * @return PensaoAlimenticia
     * @throws \Exception
     */
    public function criar(PensaoAlimenticia $pensaoAlimenticia, Resource $arquivoDecisaoJudicial = null, Dependencia $dependente = null)
    {
        if ($arquivoDecisaoJudicial instanceof Resource) {
            $document = $this->gerarDocumentoPensaoAlimenticia($arquivoDecisaoJudicial, $pensaoAlimenticia);
            $pensaoAlimenticia->setArquivoDecisaoJudicial($document);
        }

        if ($dependente instanceof Dependencia) {
            $pensaoAlimenticia->setDependente($dependente);
        }

        $this->persistenceManager->add($pensaoAlimenticia);

        return $pensaoAlimenticia;
    }

    /**
     * @param Company $company
     */
    public function getTotalPensaoAlimenticiaADescontarNoContrachequeByCompany(Company $company)
    {
        $pensoesAtivasQueDescontamNoContracheque = $this->pensaoAlimenticiaRepository->findPensoesAtivasQueDevemDescontarNoContrachequeByCompany($company);
        return array_reduce($pensoesAtivasQueDescontamNoContracheque->toArray(), function ($total, PensaoAlimenticia $pensaoAlimenticia){
            $total += $pensaoAlimenticia->getValor();
            return $total;
        }, 0);
    }

    /**
     * @param Company\Partner $partner
     * @return float
     */
    public function getTotalPensaoAlimenticiaADescontarNoContrachequeByPartner(Company\Partner $partner)
    {
        return $this->pensaoAlimenticiaRepository->getValorTotalPensoesAtivasQueDevemDescontarNoContrachequeByPartner($partner);
    }

    /**
     * @param PensaoAlimenticia $pensaoAlimenticiaUpdated
     * @param Resource|null $arquivoDecisaoJudicial
     * @param bool $removeArquivoDecisaoJudicial
     * @param Dependencia|null $dependente
     * @return PensaoAlimenticia
     * @throws \Exception
     */
    public function atualizar(
        PensaoAlimenticia $pensaoAlimenticiaUpdated,
        Resource $arquivoDecisaoJudicial = null,
        $removeArquivoDecisaoJudicial = false,
        Dependencia $dependente = null
    )
    {
        if ($removeArquivoDecisaoJudicial) {
            $pensaoAlimenticiaUpdated->setArquivoDecisaoJudicial(null);
        }

        if ($arquivoDecisaoJudicial instanceof Resource) {
            $document = $this->gerarDocumentoPensaoAlimenticia($arquivoDecisaoJudicial, $pensaoAlimenticiaUpdated);
            $pensaoAlimenticiaUpdated->setArquivoDecisaoJudicial($document);
        }

        if ($dependente instanceof Dependencia) {
            $pensaoAlimenticiaUpdated->setDependente($dependente);
        }

        $this->persistenceManager->update($pensaoAlimenticiaUpdated);

        return $pensaoAlimenticiaUpdated;
    }

    /**
     * @param PensaoAlimenticia $pensaoAlimenticia
     * @param Resource $arquivoDecisaoJudicial
     * @return Document
     * @throws \Exception
     */
    public function gerarDocumentoPensaoAlimenticia(Resource $arquivoDecisaoJudicial, PensaoAlimenticia $pensaoAlimenticia)
    {
        $document = $this->criarDocumentDecisaoJudicial($pensaoAlimenticia);
        $document->uploadResource($arquivoDecisaoJudicial);
        $pensaoAlimenticia->setArquivoDecisaoJudicial($document);

        return $document;
    }

    /**
     * @param PensaoAlimenticia $pensaoAlimenticia
     * @return Document
     */
    public function criarDocumentDecisaoJudicial(PensaoAlimenticia $pensaoAlimenticia)
    {
        $metadata = [
            [
                'key' => 'year',
                'type' => DocumentMetadataType::STRING,
                'value' => Carbon::today()->year
            ],
            [
                'key' => 'date',
                'type' => DocumentMetadataType::STRING,
                'value' => Carbon::today()->format('YmdHis')
            ],
            [
                'key' => 'partner_main_document',
                'type' => DocumentMetadataType::STRING,
                'value' => $pensaoAlimenticia->getPartner()->getMainDocument()
            ]
        ];

        return $this->documentFactory->createGeneric(
            $pensaoAlimenticia->getCompany(),
            DocumentType::DECISAO_JUDICIAL_PENSAO_ALIMENTICIA,
            $metadata);
    }

    /**
     * Busca uma dependência pelo ID
     * 
     * @param string $dependenciaId
     * @return Dependencia
     */
    public function findDependencia($dependenciaId)
    {
        return $this->dependenciaRepository->findByIdentifier($dependenciaId);
    }

    /**
     * Encontra dependências que são válidas para pensão alimentícia.
     * Elas devem ter CPF, nome completo e data de nascimento.
     * 
     * @param Partner $partner
     * @return array
     */
    public function findDependenciasValidasParaPensao(Partner $partner)
    {
        $dependencias = $this->dependenciaRepository->findByPartner($partner);
        return array_filter($dependencias->toArray(), function(Dependencia $dependencia) {
            return $dependencia->isValid();
        });
    }
}
