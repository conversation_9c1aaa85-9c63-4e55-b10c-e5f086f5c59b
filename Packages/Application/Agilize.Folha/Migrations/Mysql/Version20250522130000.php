<?php

namespace Agilize\Folha\Migrations\Mysql;

use Doctrine\DBAL\Migrations\AbstractMigration;
use Doctrine\DBAL\Schema\Schema;

/**
 * Corrige o relacionamento entre PensaoAlimenticia e Dependencia
 * Muda de ManyToOne para ManyToMany (uma pensão pode ter múltiplos dependentes)
 */
class Version20250522130000 extends AbstractMigration
{
    /**
     * @param Schema $schema
     */
    public function up(Schema $schema)
    {
        $this->abortIf($this->connection->getDatabasePlatform()->getName() != "mysql");

        // Remove a coluna dependente da tabela principal (relacionamento ManyToOne incorreto)
        $this->addSql("ALTER TABLE agilize_folha_pensaoalimenticia_domain_model_pensaoalimenticia DROP FOREIGN KEY FK_AA496F343EDA8DDC");
        $this->addSql("DROP INDEX IDX_AA496F343EDA8DDC ON agilize_folha_pensaoalimenticia_domain_model_pensaoalimenticia");
        $this->addSql("ALTER TABLE agilize_folha_pensaoalimenticia_domain_model_pensaoalimenticia DROP dependente");

        // Cria tabela de relacionamento ManyToMany
        $this->addSql("CREATE TABLE agilize_folha_pensaoalimenticia_dependencias (
            pensaoalimenticia_id VARCHAR(40) NOT NULL,
            dependencia_id VARCHAR(40) NOT NULL,
            PRIMARY KEY (pensaoalimenticia_id, dependencia_id),
            INDEX IDX_PENSAO_DEP_PENSAO (pensaoalimenticia_id),
            INDEX IDX_PENSAO_DEP_DEP (dependencia_id),
            CONSTRAINT FK_PENSAO_DEP_PENSAO 
                FOREIGN KEY (pensaoalimenticia_id) 
                REFERENCES agilize_folha_pensaoalimenticia_domain_model_pensaoalimenticia (persistence_object_identifier),
            CONSTRAINT FK_PENSAO_DEP_DEP 
                FOREIGN KEY (dependencia_id) 
                REFERENCES apimenti_account_domain_model_naturalperson_dependencia (persistence_object_identifier)
        ) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB");
    }

    /**
     * @param Schema $schema
     */
    public function down(Schema $schema)
    {
        $this->abortIf($this->connection->getDatabasePlatform()->getName() != "mysql");

        // Remove a tabela de relacionamento ManyToMany
        $this->addSql("DROP TABLE agilize_folha_pensaoalimenticia_dependencias");

        // Restaura a coluna dependente (relacionamento ManyToOne)
        $this->addSql("ALTER TABLE agilize_folha_pensaoalimenticia_domain_model_pensaoalimenticia ADD dependente VARCHAR(40) DEFAULT NULL");
        $this->addSql("ALTER TABLE agilize_folha_pensaoalimenticia_domain_model_pensaoalimenticia ADD CONSTRAINT FK_AA496F343EDA8DDC FOREIGN KEY (dependente) REFERENCES apimenti_account_domain_model_naturalperson_dependencia (persistence_object_identifier)");
        $this->addSql("CREATE INDEX IDX_AA496F343EDA8DDC ON agilize_folha_pensaoalimenticia_domain_model_pensaoalimenticia (dependente)");
    }
}
