<?php

namespace Agilize\Api\V1\Controller\Companies\Folha\PensaoAlimenticia;

use Agilize\Administrative\Exception\ApiException;
use Agilize\Api\V1\Controller\BaseController;
use Agilize\Dms\Domain\Model\Document;
use Agilize\Folha\PensaoAlimenticia\Domain\Model\PensaoAlimenticia;
use Agilize\Folha\PensaoAlimenticia\Service\PensaoAlimenticiaService;
use Apimenti\Account\Domain\Model\Company;
use Apimenti\Account\Domain\Model\Company\Partner;
use Apimenti\Account\Domain\Model\NaturalPerson\Vinculo;
use Apimenti\Account\Domain\Service\CompanyService;
use Apimenti\Account\Domain\Service\NaturalPerson\VinculoService;
use Apimenti\Account\Service\ProlaboreService;
use Apimenti\Util\Helper\JsonViewHelper;
use Carbon\Carbon;
use Doctrine\Common\Collections\ArrayCollection;
use MS\RH\Partner\Domain\Factory\RHPensaoAlimenticiaChangeFactory;
use MS\RH\Partner\Domain\Model\RHPartnerChanges;
use MS\RH\Partner\Facade\RHPartnerChangesFacade;
use TYPO3\Flow\Annotations as Flow;
use TYPO3\Flow\Property\TypeConverter\PersistentObjectConverter;
use TYPO3\Flow\Resource\Resource;

class PensaoAlimenticiaController extends BaseController
{
    protected $resourceArgumentName = 'pensaoAlimenticia';

    /**
     * @var \TYPO3\Flow\Mvc\View\JsonView
     */
    protected $view;

    /**
     * @var CompanyService
     * @Flow\Inject
     */
    protected $companyService;

    /**
     * @var PensaoAlimenticiaService
     * @Flow\Inject
     */
    protected $pensaoAlimenticiaService;

    /**
     * @var ProlaboreService
     * @Flow\Inject
     */
    protected $prolaboreService;

    /**
     * @var VinculoService
     * @Flow\Inject
     */
    protected $vinculoService;

    /**
     * @var RHPensaoAlimenticiaChangeFactory
     * @Flow\Inject
     */
    protected $rhPensaoFactory;

    /**
     * @var RHPartnerChangesFacade
     * @Flow\Inject
     */
    protected $rhPartnerHistoryFacade;

    /**
     * @param Company $company
     * @param Partner $partner
     */
    public function listAction(
        Company $company,
        Partner $partner
    )
    {
        $pensaoAlimenticia = $this->pensaoAlimenticiaService->listarPensoesByPartner($company, $partner);

        $this->view->setVariablesToRender(['pensaoAlimenticia']);

        $this->view->setConfiguration([
            'pensaoAlimenticia' => $this->getListConfiguration()
        ]);

        $this->view->assign('pensaoAlimenticia', $pensaoAlimenticia);
    }

    /**
     * @param PensaoAlimenticia $pensaoAlimenticia
     */
    public function showAction(PensaoAlimenticia $pensaoAlimenticia)
    {
        try {
            $this->view->setVariablesToRender(['pensaoAlimenticia']);

            $this->view->setConfiguration([
                'pensaoAlimenticia' => $this->getItemConfiguration()
            ]);

            $this->view->assign('pensaoAlimenticia', $pensaoAlimenticia);
        } catch (\Exception $exception) {
            $this->response->setStatus(500);

            $this->view->setVariablesToRender(['error']);
            $this->view->assign('error', JsonViewHelper::getErrorArray($exception, "Benefício não disponível!"));
        }
    }

    /**
     * @param Company $company
     * @param Partner $partner
     * @param PensaoAlimenticia $pensaoAlimenticia
     * @param Resource $arquivoDecisaoJudicial
     */
    public function createAction(
        Company $company,
        Partner $partner,
        PensaoAlimenticia $pensaoAlimenticia,
        Resource $arquivoDecisaoJudicial = null
    )
    {
        try {
            $this->throwCanHavePensaoAlimentica($company, $pensaoAlimenticia);
            $this->throwExceptionOnHasNoVinculo($pensaoAlimenticia);
            $this->throwPensaoAlimenticiaSuperiorSalario($pensaoAlimenticia);

            // Obtém o dependente da requisição, se existir
            $dependenteId = $this->request->hasArgument('dependente') ? $this->request->getArgument('dependente') : null;
            $dependente = null;

            if ($dependenteId) {
                $dependente = $this->pensaoAlimenticiaService->findDependencia($dependenteId);
                if (!$dependente instanceof \Apimenti\Account\Domain\Model\NaturalPerson\Dependencia) {
                    throw new ApiException('Dependente não encontrado.', **********);
                }
            }

            $pensaoAlimenticia = $this->pensaoAlimenticiaService->criar($pensaoAlimenticia, $arquivoDecisaoJudicial, $dependente);

            $rhPartnerChangesDto = $this->rhPensaoFactory->create($company, $partner->getPerson(), $pensaoAlimenticia, RHPartnerChanges::CREATE);
            $this->rhPartnerHistoryFacade->dispatchPartnerChangesJob(new ArrayCollection([$rhPartnerChangesDto]));

            $this->view->setVariablesToRender(['pensaoAlimenticia']);

            $this->view->setConfiguration([
                'pensaoAlimenticia' => $this->getItemConfiguration()
            ]);

            $this->view->assign('pensaoAlimenticia', $pensaoAlimenticia);

        } catch (ApiException $exception) {
            $exception->sendError($this->response, $this->view, ApiException::HTTP_BAD_REQUEST);
        } catch (\Exception $e) {
            $this->view->setVariablesToRender(['response']);
            $this->response->setStatus(500);
            $this->view->assign('response', ['error' => $e->getMessage()]);
        }
    }

    public function initializeUpdateAction()
    {
        $this->arguments->getArgument('pensaoAlimenticiaUpdated')
            ->getPropertyMappingConfiguration()
            ->setTypeConverterOption(
                PersistentObjectConverter::class,
                PersistentObjectConverter::CONFIGURATION_MODIFICATION_ALLOWED,
                true
            )
            ->allowAllProperties();
    }

    /**
     * @param PensaoAlimenticia $pensaoAlimenticia
     * @param PensaoAlimenticia $pensaoAlimenticiaUpdated
     * @param bool $removeArquivoDecisaoJudicial
     * @param Resource $arquivoDecisaoJudicial
     */
    public function updateAction(
        PensaoAlimenticia $pensaoAlimenticia,
        PensaoAlimenticia $pensaoAlimenticiaUpdated,
        $removeArquivoDecisaoJudicial = false,
        Resource $arquivoDecisaoJudicial = null
    )
    {
        try {
            $company = $pensaoAlimenticia->getCompany();
            $this->throwCanHavePensaoAlimentica($company, $pensaoAlimenticiaUpdated);

            $this->throwPensaoAlimenticiaSuperiorSalario($pensaoAlimenticiaUpdated);

            // Obtém o dependente da requisição, se existir
            $dependenteId = $this->request->hasArgument('dependente') ? $this->request->getArgument('dependente') : null;
            $dependente = null;

            if ($dependenteId) {
                $dependente = $this->pensaoAlimenticiaService->findDependencia($dependenteId);
                if (!$dependente instanceof \Apimenti\Account\Domain\Model\NaturalPerson\Dependencia) {
                    throw new ApiException('Dependente não encontrado.', **********);
                }
            }

            $rhPartnerChangesDto = $this->rhPensaoFactory->create(
                $company,
                $pensaoAlimenticia->getPartner()->getPerson(),
                $pensaoAlimenticiaUpdated,
                RHPartnerChanges::UPDATE,
                $pensaoAlimenticia
            );
            $this->rhPartnerHistoryFacade->dispatchPartnerChangesJob(new ArrayCollection([$rhPartnerChangesDto]));

            $pensaoAlimenticia = $this->pensaoAlimenticiaService->atualizar(
                $pensaoAlimenticiaUpdated,
                $arquivoDecisaoJudicial,
                $removeArquivoDecisaoJudicial,
                $dependente
            );

            $this->view->setVariablesToRender(['pensaoAlimenticia']);

            $this->view->setConfiguration([
                'pensaoAlimenticia' => $this->getItemConfiguration()
            ]);

            $this->view->assign('pensaoAlimenticia', $pensaoAlimenticia);
        } catch (ApiException $exception) {
            $exception->sendError($this->response, $this->view, ApiException::HTTP_BAD_REQUEST);
        } catch (\Exception $e) {
            $this->view->setVariablesToRender(['response']);
            $this->response->setStatus(500);
            $this->view->assign('response', ['error' => $e->getMessage()]);
        }
    }

    /**
     * @param PensaoAlimenticia $pensaoAlimenticia
     */
    public function encerrarAction(PensaoAlimenticia $pensaoAlimenticia)
    {
        try {
            $this->pensaoAlimenticiaService->encerrar($pensaoAlimenticia);

            $this->response->setStatus(200);
        } catch (ApiException $exception) {
            $exception->sendError($this->response, $this->view, ApiException::HTTP_BAD_REQUEST);
        } catch (\Exception $e) {
            $this->view->setVariablesToRender(['response']);
            $this->response->setStatus(500);
            $this->view->assign('response', ['error' => $e->getMessage()]);
        }
    }

    /**
     * @param PensaoAlimenticia $pensaoAlimenticia
     */
    public function downloadAction(PensaoAlimenticia $pensaoAlimenticia)
    {
        try {
            if ($pensaoAlimenticia->getArquivoDecisaoJudicial() instanceof Document) {
                $this->redirectToUri(
                    $pensaoAlimenticia->getArquivoDecisaoJudicial()->getFileUrl()
                );
            }
        } catch (\Exception $exception) {
            $this->response->setStatus(500);

            $this->view->setVariablesToRender(['error']);
            $this->view->assign('error', JsonViewHelper::getErrorArray($exception, "Benefício não disponível!"));
        }
    }

    /**
     * @param Company $company
     * @param PensaoAlimenticia $pensaoAlimenticia
     * @throws ApiException
     */
    private function throwCanHavePensaoAlimentica
    (
        Company $company,
        PensaoAlimenticia $pensaoAlimenticia
    )
    {
        $fatorRActived = $this->companyService->isAutomaticFatorR($company);
        if ($fatorRActived && $pensaoAlimenticia->isDecisaoJudicial()) {
            throw new ApiException(
                'Não é possivel cadastrar pensão alimentícia para empresas que possuem o recurso do fator R automático ativo.',
                1593616586
            );
        }
    }

    /**
     * @param PensaoAlimenticia $pensaoAlimenticia
     * @throws ApiException
     */
    private function throwPensaoAlimenticiaSuperiorSalario(PensaoAlimenticia $pensaoAlimenticia)
    {
        $partner = $pensaoAlimenticia->getPartner();

        $vinculo = $this->vinculoService->getVinculoValidoDeProlaboreDeUmPartner($partner);

        if (!$vinculo instanceof Vinculo) {
            return;
        }

        if ($pensaoAlimenticia->isDecisaoJudicial() && !$this->prolaboreService->valorTotalLiquidoProlaboreMaiorQueOutrosDescontos(
                $partner,
                Carbon::today(),
                $vinculo->getValor(),
                null,
                $pensaoAlimenticia->getValor())
        ) {
            throw new ApiException(
                'Valor da pensão alimentícia não pode ser maior que o valor do pró-labore líquido.',
                1593784531
            );
        }
    }

    /**
     * @param PensaoAlimenticia $pensaoAlimenticia
     * @return void
     */
    private function throwExceptionOnHasNoVinculo(PensaoAlimenticia $pensaoAlimenticia)
    {
        $partner = $pensaoAlimenticia->getPartner();
        $vinculo = $this->vinculoService->getVinculoValidoDeProlaboreDeUmPartner($partner);
        if ($pensaoAlimenticia->isDecisaoJudicial() && $pensaoAlimenticia->getDeveDescontarNoContracheque() && !$vinculo instanceof Vinculo) {
            throw new \Exception('Não é possível criar uma pensão alimentícia com decisão judicial descontando no contracheque sem ter um pró-labore cadastrado.', 1686663519);
        }
    }

    /**
     * @return array
     */
    private function getListConfiguration()
    {
        return [
            '_descendAll' => $this->getItemConfiguration()
        ];
    }

    /**
     * @return array
     */
    private function getItemConfiguration()
    {
        return [
            '_only' => [
                'partner',
                'valor',
                'deveDescontarNoContracheque',
                'tipoPensaoAlimenticiaObject',
                'arquivoDecisaoJudicial',
                'company',
                'encerradoEm',
                'dependente'
            ],
            '_exposeObjectIdentifier' => true,
            '_exposedObjectIdentifierKey' => '__identity',
            '_descend' => [
                'partner' => [
                    '_exposeObjectIdentifier' => true,
                    '_exposedObjectIdentifierKey' => '__identity',
                    '_only' => ['person', 'share', 'isActive'],
                    '_descend' => [
                        'person' => [
                            '_exposeObjectIdentifier' => true,
                            '_exposedObjectIdentifierKey' => '__identity',
                            '_only' => ['__identity', 'name'],
                        ],
                    ]
                ],
                'tipoPensaoAlimenticiaObject' => [],
                'arquivoDecisaoJudicial' => [
                    '_exposeObjectIdentifier' => true,
                    '_exposedObjectIdentifierKey' => '__identity',
                    '_only' => [
                        '__identity',
                    ]
                ],
                'company' => [
                    '_exposeObjectIdentifier' => true,
                    '_exposedObjectIdentifierKey' => '__identity',
                    '_only' => ['__identity', 'name', 'cnpj']
                ],
                'encerradoEm' => [],
                'dependente' => [
                    '_exposeObjectIdentifier' => true,
                    '_exposedObjectIdentifierKey' => '__identity',
                    '_only' => ['__identity'],
                    '_descend' => [
                        'dependente' => [
                            '_exposeObjectIdentifier' => true,
                            '_exposedObjectIdentifierKey' => '__identity',
                            '_only' => ['__identity', 'name', 'cpf', 'birthDate'],
                        ]
                    ]
                ]
            ]
        ];
    }

    /**
     * @return array
     */
    private function getHistoriesConfiguration()
    {
        return [
            '_descendAll' => $this->getHistoryConfiguration()
        ];
    }

    /**
     * @return array
     */
    private function getHistoryConfiguration()
    {
        return [
            '_only' => [
                'user',
                'company',
                'operadora',
                'tipoBeneficio',
                'beneficiarios',
                'createdAt'
            ],
            '_descend' => [
                'user' => [
                    '_only' => ['name']
                ],
                'createdAt' => []
            ]
        ];
    }

    /**
     * Lista os dependentes válidos para associação com pensão alimentícia
     *
     * @param Company $company
     * @param Partner $partner
     */
    public function dependentesAction(
        Company $company,
        Partner $partner
    )
    {
        try {
            $dependencias = $this->pensaoAlimenticiaService->findDependenciasValidasParaPensao($partner);

            $this->view->setVariablesToRender(['dependencias']);

            $this->view->setConfiguration([
                'dependencias' => [
                    '_descendAll' => [
                        '_exposeObjectIdentifier' => true,
                        '_exposedObjectIdentifierKey' => '__identity',
                        '_only' => ['dependente', '__identity'],
                        '_descend' => [
                            'dependente' => [
                                '_exposeObjectIdentifier' => true,
                                '_exposedObjectIdentifierKey' => '__identity',
                                '_only' => ['__identity', 'name', 'cpf', 'birthDate'],
                            ]
                        ]
                    ]
                ]
            ]);

            $this->view->assign('dependencias', $dependencias);
        } catch (\Exception $e) {
            $this->view->setVariablesToRender(array('response'));
            $this->response->setStatus(500);
            $this->view->assign('response', array('error' => $e->getMessage()));
        }
    }
}
