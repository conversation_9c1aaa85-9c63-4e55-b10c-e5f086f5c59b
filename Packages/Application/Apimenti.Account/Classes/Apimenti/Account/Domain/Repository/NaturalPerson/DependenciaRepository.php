<?php

namespace Apimenti\Account\Domain\Repository\NaturalPerson;

use Apimenti\Account\Domain\Model\Company\Partner;
use TYPO3\Flow\Annotations as Flow;
use TYPO3\Flow\Persistence\Repository;

/**
 * @Flow\Scope("singleton")
 */
class DependenciaRepository extends Repository
{
    /**
     * @var array
     */
    protected $defaultOrderings = array(
        'createdAt' => \TYPO3\Flow\Persistence\QueryInterface::ORDER_DESCENDING
    );

    /**
     * Busca dependências por parceiro
     *
     * @param Partner $partner
     * @return array
     */
    public function findByPartner(Partner $partner)
    {
        $query = $this->createQuery();

        $query->matching(
            $query->logicalAnd([
                $query->equals('responsavel.partners', $partner)
            ])
        );

        return $query->execute()->toArray();
    }

    /**
     * Busca dependências válidas para pensão alimentícia por parceiro
     * Filtra no banco de dados dependentes que possuem CPF, nome e data de nascimento
     * Optimized database-level filtering to avoid excessive foreach loops
     *
     * @param Partner $partner
     * @return array
     */
    public function findValidDependentsForAlimonyByPartner(Partner $partner)
    {
        $query = $this->createQuery();

        $query->matching(
            $query->logicalAnd([
                $query->equals('responsavel.partners', $partner),
                $query->logicalNot($query->equals('dependente.cpf', null)),
                $query->logicalNot($query->equals('dependente.cpf', '')),
                $query->logicalNot($query->equals('dependente.name', null)),
                $query->logicalNot($query->equals('dependente.name', '')),
                $query->logicalNot($query->equals('dependente.birthDate', null))
            ])
        );

        return $query->execute()->toArray();
    }
}