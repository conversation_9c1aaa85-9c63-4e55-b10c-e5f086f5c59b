<?php

namespace Apimenti\Account\Domain\Repository\NaturalPerson;

use Apimenti\Account\Domain\Model\Company\Partner;
use TYPO3\Flow\Annotations as Flow;
use TYPO3\Flow\Persistence\Repository;

/**
 * @Flow\Scope("singleton")
 */
class DependenciaRepository extends Repository
{
    /**
     * @var array
     */
    protected $defaultOrderings = array(
        'createdAt' => \TYPO3\Flow\Persistence\QueryInterface::ORDER_DESCENDING
    );

    /**
     * Busca dependências por parceiro
     *
     * @param Partner $partner
     * @return array
     */
    public function findByPartner(Partner $partner)
    {
        $query = $this->createQuery();
        $queryBuilder = $query->getQueryBuilder();
        $queryBuilder
            ->select('d')
            ->from($this->getEntityClassName(), 'd')
            ->join('d.responsavel', 'r')
            ->join('r.partners', 'p')
            ->where('p = :partner')
            ->setParameter('partner', $partner);

        return $queryBuilder->getQuery()->getResult();
    }

    /**
     * Busca dependências válidas para pensão alimentícia por parceiro
     * Filtra no banco de dados dependentes que possuem CPF, nome e data de nascimento
     * Optimized database-level filtering to avoid excessive foreach loops
     *
     * @param Partner $partner
     * @return array
     */
    public function findValidDependentsForAlimonyByPartner(Partner $partner)
    {
        $query = $this->createQuery();
        $queryBuilder = $query->getQueryBuilder();
        $queryBuilder
            ->select('d')
            ->from($this->getEntityClassName(), 'd')
            ->join('d.responsavel', 'r')
            ->join('r.partners', 'p')
            ->join('d.dependente', 'dep')
            ->where('p = :partner')
            ->andWhere('dep.cpf IS NOT NULL')
            ->andWhere('dep.cpf != :emptyCpf')
            ->andWhere('dep.name IS NOT NULL')
            ->andWhere('dep.name != :emptyName')
            ->andWhere('dep.birthDate IS NOT NULL')
            ->setParameters([
                'partner' => $partner,
                'emptyCpf' => '',
                'emptyName' => ''
            ]);

        return $queryBuilder->getQuery()->getResult();
    }
}