<?php

namespace Apimenti\Account\Domain\Model\NaturalPerson;

use Apimenti\Account\Domain\Model\NaturalPerson;
use Apimenti\Util\Domain\Model\TimestampableEntity;
use Doctrine\ORM\Mapping as ORM;
use TYPO3\Flow\Annotations as Flow;

/**
 * @Flow\Entity
 * <AUTHOR> <<EMAIL>>
 */
class Dependencia
{
    use TimestampableEntity;

    /**
     * @var NaturalPerson
     * @ORM\ManyToOne
     */
    protected $responsavel;

    /**
     * @var NaturalPerson
     * @ORM\ManyToOne
     * @Flow\Identity
     */
    protected $dependente;

    /**
     * @var DependenciaTipo
     * @ORM\ManyToOne
     */
    protected $dependenciaTipo;

    /**
     * @var bool
     */
    protected $temDependenciaDeIr;

    /**
     * @var bool
     */
    protected $temDependenciaDeSalarioFamilia;

    /**
     * @var bool
     */
    protected $temIncapacidadeFisicaOuMental;

    /**
     * Dependencia constructor.
     * @param NaturalPerson $responsavel
     * @param NaturalPerson $dependente
     * @param DependenciaTipo $dependenciaTipo
     * @param $temDependenciaDeIr
     * @param $temDependenciaDeSalarioFamilia
     * @param $temIncapacidadeFisicaOuMental
     */
    public function __construct(NaturalPerson $responsavel, NaturalPerson $dependente, DependenciaTipo $dependenciaTipo, $temDependenciaDeIr, $temDependenciaDeSalarioFamilia, $temIncapacidadeFisicaOuMental)
    {
        $this->responsavel = $responsavel;
        $this->dependente = $dependente;
        $this->dependenciaTipo = $dependenciaTipo;
        $this->temDependenciaDeIr = $temDependenciaDeIr;
        $this->temDependenciaDeSalarioFamilia = $temDependenciaDeSalarioFamilia;
        $this->temIncapacidadeFisicaOuMental = $temIncapacidadeFisicaOuMental;
    }


    /**
     * @return NaturalPerson
     */
    public function getResponsavel()
    {
        return $this->responsavel;
    }

    /**
     * @return NaturalPerson
     */
    public function getDependente()
    {
        return $this->dependente;
    }

    /**
     * @return DependenciaTipo
     */
    public function getDependenciaTipo()
    {
        return $this->dependenciaTipo;
    }

    /**
     * @return bool
     */
    public function getTemDependenciaDeIr()
    {
        return $this->temDependenciaDeIr;
    }

    /**
     * @return bool
     */
    public function isTemDependenciaDeSalarioFamilia()
    {
        return $this->temDependenciaDeSalarioFamilia;
    }

    /**
     * @return bool
     */
    public function isTemIncapacidadeFisicaOuMental()
    {
        return $this->temIncapacidadeFisicaOuMental;
    }

    /**
     * @param NaturalPerson $dependente
     */
    public function setDependente($dependente)
    {
        $this->dependente = $dependente;
    }

    /**
     * @param DependenciaTipo $dependenciaTipo
     */
    public function setDependenciaTipo($dependenciaTipo)
    {
        $this->dependenciaTipo = $dependenciaTipo;
    }

    /**
     * @param bool $temDependenciaDeIr
     */
    public function setTemDependenciaDeIr($temDependenciaDeIr)
    {
        $this->temDependenciaDeIr = $temDependenciaDeIr;
    }

    /**
     * @param bool $temDependenciaDeSalarioFamilia
     */
    public function setTemDependenciaDeSalarioFamilia($temDependenciaDeSalarioFamilia)
    {
        $this->temDependenciaDeSalarioFamilia = $temDependenciaDeSalarioFamilia;
    }

    /**
     * @param bool $temIncapacidadeFisicaOuMental
     */
    public function setTemIncapacidadeFisicaOuMental($temIncapacidadeFisicaOuMental)
    {
        $this->temIncapacidadeFisicaOuMental = $temIncapacidadeFisicaOuMental;
    }

    /**
     * Validates if the dependent has all required information for alimony association.
     * Following Single Responsibility Principle for validation logic.
     *
     * @return bool
     */
    public function isValid()
    {
        $dependente = $this->getDependente();

        return $dependente instanceof NaturalPerson
            && !empty($dependente->getCpf())
            && !empty($dependente->getName())
            && $dependente->getBirthDate() instanceof \DateTime;
    }
}
